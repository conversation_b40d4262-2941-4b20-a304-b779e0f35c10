const fs = require('fs');
const path = require('path');
const { getArg } = require('./getArg');

const argDir = getArg('--dir') ?? null;

if (!argDir) {
  console.error('Error: please provide a directory!');
  process.exit(1);
}

const INDEX_FILE = path.join(argDir, 'index.tsx');

const argPrefix = getArg('--prefix') ?? 'Icon';

function toPascalCase(str) {
  return str
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, (l) => l.toUpperCase())
    .replace(/\s/g, '');
}

function generateIconName(filename, subfolder = '') {
  const baseName = path.basename(filename, '.svg');
  const pascalName = toPascalCase(baseName);

  // Add Icon prefix if not already present
  if (!pascalName.startsWith(argPrefix)) {
    return `${argPrefix}${pascalName}`;
  }

  return pascalName;
}

function scanDirectory(dir, baseDir = dir) {
  const items = fs.readdirSync(dir, { withFileTypes: true });
  const result = {};

  for (const item of items) {
    const fullPath = path.join(dir, item.name);

    if (item.isDirectory()) {
      // Recursively scan subdirectories
      const subResult = scanDirectory(fullPath, baseDir);

      Object.assign(result, subResult);
    } else if (item.isFile() && item.name.endsWith('.svg')) {
      const relativePath = path.relative(baseDir, fullPath);
      const subfolder = path.dirname(relativePath);
      const isInRoot = subfolder === '.';

      const groupKey = isInRoot ? 'root' : subfolder;

      if (!result[groupKey]) {
        result[groupKey] = [];
      }

      result[groupKey].push({
        filename: item.name,
        relativePath: relativePath.replace(/\\/g, '/'),
        iconName: generateIconName(item.name),
      });
    }
  }

  return result;
}

function generateIndexContent(iconGroups) {
  let content = '';

  // Sort groups - root first, then alphabetically
  const sortedGroups = Object.keys(iconGroups).sort((a, b) => {
    if (a === 'root') return -1;
    if (b === 'root') return 1;

    return a.localeCompare(b);
  });

  for (const group of sortedGroups) {
    const icons = iconGroups[group];

    if (group !== 'root') {
      content += `\n// ${group.charAt(0).toUpperCase() + group.slice(1)} icons\n`;
    }

    // Sort icons alphabetically
    icons.sort((a, b) => a.iconName.localeCompare(b.iconName));

    for (const icon of icons) {
      content += `export { default as ${icon.iconName} } from './${icon.relativePath}';\n`;
    }
  }

  return content;
}

function main() {
  try {
    console.log('Scanning icons directory...');

    const iconGroups = scanDirectory(argDir);

    // Filter out empty groups and the script file itself
    Object.keys(iconGroups).forEach((key) => {
      iconGroups[key] = iconGroups[key].filter(
        (icon) =>
          !icon.filename.includes('generate-index') &&
          !icon.filename.includes('index.tsx'),
      );
      if (iconGroups[key].length === 0) {
        delete iconGroups[key];
      }
    });

    const content = generateIndexContent(iconGroups);

    fs.writeFileSync(INDEX_FILE, content);

    console.log(`✅ Generated ${INDEX_FILE}`);
    console.log(
      `📊 Found ${Object.values(iconGroups).flat().length} icons in ${Object.keys(iconGroups).length} groups`,
    );

    // Log summary
    for (const [group, icons] of Object.entries(iconGroups)) {
      const groupName = group === 'root' ? 'Root' : group;

      console.log(`   ${groupName}: ${icons.length} icons`);
    }
  } catch (error) {
    console.error('❌ Error generating index file:', error.message);
    process.exit(1);
  }
}

if (require.main === module) {
  main();
}

module.exports = { main };
