import styles from '../ImageAnalyzer.module.scss';
import cn from 'classnames';
import React from 'react';
import {
  IconImageRating0,
  IconImageRating1,
  IconImageRating2,
  IconImageRating3,
  IconImageRating4,
  IconImageRating5,
} from '@icons/index';

type RatingProps = {
  rating: number;
  isTooltip: boolean;
};

export const Rating = ({ rating, isTooltip }: RatingProps) => {
  let IconRationg: React.ElementType;

  switch (rating) {
    case 1:
      IconRationg = IconImageRating1;
      break;
    case 2:
      IconRationg = IconImageRating2;
      break;
    case 3:
      IconRationg = IconImageRating3;
      break;
    case 4:
      IconRationg = IconImageRating4;
      break;
    case 5:
      IconRationg = IconImageRating5;
      break;
    case 0:
    default:
      IconRationg = IconImageRating0;
      break;
  }

  return (
    <IconRationg
      className={cn(styles['rating-icon'], styles[`rating-icon-${rating}`], {
        [styles['rating-icon-main']]: !isTooltip,
      })}
    />
  );
};
