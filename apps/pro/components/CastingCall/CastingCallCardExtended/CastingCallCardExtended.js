'use client';
import React, { memo, useState } from 'react';
import styles from './CastingCallCardExtended.module.scss';
import cn from 'classnames';
import Link from 'next/link';
import {
  Button,
  CastingCallIcon,
  CastingCallStatus,
  CategoryList,
  ModalBoost,
  PaymentLabel,
} from '@components';
import dayjs from 'dayjs';
import { STATUS, TYPE } from '@constants/castingCalls';

const CastingCallCardExtended = ({
  mainCategory,
  additionalCategories = [],
  id,
  title,
  description,
  location,
  rolesCount = 0,
  expires = 'soon',
  isOnline = false,
  isEasyToApply = false,
  hot = false,
  paymentAmount,
  paymentPeriod,
  paymentCurrency,
  status,
  candidates,
  slug,
  isBoostedDefault,
  type,
  isExpired,
}) => {
  const [showBoostModal, setShowBoostModal] = useState(false);
  const [isBoosted, setIsBoosted] = useState(isBoostedDefault);

  const toggleShowBoostModal = () => {
    setShowBoostModal(!showBoostModal);
  };

  const onBoostSuccess = () => {
    setIsBoosted(true);
  };

  return (
    <>
      <div style={{ position: 'relative' }}>
        <Link
          className={cn(styles['card-container'], {
            [styles.inactive]:
              status === STATUS.Declined || status === STATUS.Deactivated,
            [styles.active]: status === STATUS.Approved,
          })}
          href={`/castingcall/${id}`}
        >
          <div className={styles['card-left']}>
            <div className={styles.desktop}>
              <div className={styles['card-icon-container']}>
                <CastingCallIcon
                  title={title}
                  slug={slug}
                  src={`/assets/icons/categories/icon-${slug}.svg`}
                />
              </div>
              <span className={styles.title}>{title}</span>
              <div className={styles['card-details']}>
                {hot && (
                  <>
                    <span className={styles.hot}>HOT</span>
                    <span className={styles['dot-divider']}></span>
                  </>
                )}
                {location && (
                  <span className={styles.location}>{location}</span>
                )}
                <span className={cn(styles.expires, styles['dot-divider'])}>
                  {isExpired ? (
                    <span className={styles.expired}>Expired</span>
                  ) : (
                    <span>Expires on {dayjs(expires).format('M/D/YYYY')}</span>
                  )}
                </span>
                {(paymentAmount || paymentPeriod === 'TFP') && (
                  <span className={styles['dot-divider']}>
                    <PaymentLabel
                      paymentAmount={paymentAmount}
                      paymentPeriod={paymentPeriod}
                      paymentCurrency={paymentCurrency}
                      type="text"
                      inline
                    />
                  </span>
                )}
              </div>
            </div>
            <div className={styles.mobile}>
              <CastingCallIcon
                title={title}
                slug={slug}
                src={`/assets/icons/categories/icon-${slug}.svg`}
              />
              <div>
                <div>
                  {hot && <span className={styles.hot}>HOT</span>}
                  <span
                    className={cn(
                      styles.expires,
                      hot ? styles['dot-divider'] : '',
                    )}
                  >
                    {isExpired ? (
                      <span className={styles.expired}>Expired</span>
                    ) : (
                      <span>
                        Expires on {dayjs(expires).format('M/D/YYYY')}
                      </span>
                    )}
                  </span>
                </div>
                <div>
                  {location && (
                    <span className={styles.location}>{location}</span>
                  )}
                  {(paymentAmount || paymentPeriod === 'TFP') && (
                    <span className={styles['dot-divider']}>
                      <PaymentLabel
                        paymentAmount={paymentAmount}
                        paymentPeriod={paymentPeriod}
                        paymentCurrency={paymentCurrency}
                        type="text"
                        inline
                      />
                    </span>
                  )}
                </div>
              </div>
            </div>
            <span className={cn(styles.title, styles.mobile)}>{title}</span>
            {description && <p className={styles.description}>{description}</p>}
            <div className={styles['category-container']}>
              <CategoryList
                mainCategory={mainCategory}
                additionalCategories={additionalCategories}
              />
            </div>
            <div className={styles['card-details-bottom']}>
              <span className={styles.roles}>
                {rolesCount} role{rolesCount > 1 ? 's' : ''} available
              </span>
              <div className={styles['badge-container']}>
                {isEasyToApply && (
                  <span
                    className={cn(styles.badge, styles['easy-apply-badge'])}
                  >
                    easy apply
                  </span>
                )}
                {isOnline && (
                  <span className={cn(styles.badge, styles['online-badge'])}>
                    online audition
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className={styles['card-right']}>
            <CastingCallStatus
              status={status}
              candidates={candidates}
              isExpired={
                isExpired &&
                status !== STATUS.Declined &&
                status !== STATUS.Deactivated
              }
            />
          </div>
        </Link>
        {status === STATUS.Approved && (
          <div className={styles['boost-btn']}>
            <Link href={`/requests/castingcall/${id}`}>view all</Link>
            <Button
              onClick={toggleShowBoostModal}
              label={isBoosted ? 'Boost Requested' : 'Boost'}
              kind="secondary"
              minWidth="200px"
              disabled={isBoosted}
            />
          </div>
        )}
      </div>
      {showBoostModal && (
        <ModalBoost
          onClose={toggleShowBoostModal}
          showWarningDefault={type !== TYPE.Web}
          defaultSelectedOptionValue={id}
          onBoostSuccess={onBoostSuccess}
        />
      )}
    </>
  );
};

export default memo(CastingCallCardExtended);
