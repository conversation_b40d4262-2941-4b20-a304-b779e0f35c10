'use client';
import React, { memo, useState } from 'react';
import styles from './ImageActions.module.scss';
import cn from 'classnames';
import { IconPhoto, IconTrash1 } from '@icons/index';

const ImageActions = ({
  onClick = () => {},
  onMakeTitleClick,
  showUploadIcon = false,
  onDelete,
  selectFile,
  actionIconSize = 'small', // small | medium | large
  isCurrentTitlePhoto,
  image,
  iconSize = 'medium', // small | medium | large
  showActions = true,
  openEditModal = () => {},
}) => {
  const [isFirstMobileClick, setIsFirstMobileClick] = useState(false);

  const onMobileClick = () => {
    if (!isFirstMobileClick) {
      setIsFirstMobileClick(true);
    } else {
      setIsFirstMobileClick(false);
      onClick();
    }
  };

  const clearIsFirstMobileClick = () => {
    if (isFirstMobileClick) {
      setIsFirstMobileClick(false);
    }
  };

  return (
    <>
      <div
        className={styles['actions-image-container']}
        onMouseLeave={clearIsFirstMobileClick}
      >
        {showActions && (
          <div
            className={cn(
              styles['profile-photo-actions'],
              styles[actionIconSize],
            )}
          >
            <div className={styles['profile-photo-actions-left']}>
              {showUploadIcon && (
                <IconPhoto
                  className={styles.icon}
                  onClick={() => {
                    clearIsFirstMobileClick();
                    selectFile();
                  }}
                />
              )}
              <img
                className={
                  isCurrentTitlePhoto ? styles['icon-title'] : styles.icon
                }
                src={`/assets/icons/icon-title-photo${
                  isCurrentTitlePhoto ? '-current' : ''
                }.svg`}
                alt="icon"
                onClick={() => {
                  clearIsFirstMobileClick();
                  onMakeTitleClick();
                }}
              />
            </div>
            <IconTrash1
              className={styles.icon}
              onClick={() => {
                clearIsFirstMobileClick();
                onDelete();
              }}
            />
          </div>
        )}
        <div
          onClick={onClick}
          className={cn(
            styles['actions-image-inner-container'],
            styles.desktop,
            styles[`actions-image-${iconSize}`],
          )}
        >
          <img className={styles['image']} src={image.url} alt="image" />
        </div>
        <div
          onClick={onMobileClick}
          className={cn(
            styles['actions-image-inner-container'],
            styles.mobile,
            styles[`actions-image-${iconSize}`],
            {
              [styles['actions-image-inner-container-main']]: !showActions,
            },
          )}
        >
          <img className={styles['image']} src={image.url} alt="image" />
        </div>

        {!showActions && (
          <button className={styles['edit-button']} onClick={openEditModal}>
            Edit photo
          </button>
        )}
      </div>
    </>
  );
};

export default memo(ImageActions);
