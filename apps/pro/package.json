{"name": "pro-allcasting-com", "version": "1.0.0", "private": true, "engines": {"node": ">=20.11.0", "npm": ">=10.2.4"}, "scripts": {"dev": "next dev -p 3200", "build": "next build", "start": "NODE_OPTIONS='-r next-logger' next start", "lint": "next lint", "lint:fix": "next lint --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "lint:all": "npm run prettier:fix & npm run lint:fix", "generate:icons": "node scripts/runGenerate.js"}, "dependencies": {"@amplitude/analytics-browser": "^2.17.8", "@analytics/google-tag-manager": "^0.6.0", "@aws-sdk/client-s3": "^3.824.0", "@bprogress/next": "^3.2.12", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@entertech/filter-service": "^1.1.6", "@livechat/widget-react": "^1.3.4", "@mui/material": "^7.1.1", "@sentry/nextjs": "^9.26.0", "analytics": "^0.8.16", "classnames": "^2.5.1", "cookies-next": "^6.0.0", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "formik": "^2.4.6", "next": "^15.3.3", "next-logger": "^5.0.1", "pino": "^9.7.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-easy-crop": "^5.4.2", "react-masonry-css": "^1.0.16", "react-tiny-popover": "^8.1.6", "sharp": "^0.34.2", "use-analytics": "^1.1.0", "yup": "^1.6.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "eslint": "^9.28.0", "eslint-config-next": "^15.3.3", "eslint-config-prettier": "^10.1.5", "eslint-plugin-unused-imports": "^4.1.4", "mini-css-extract-plugin": "^2.9.2", "prettier": "^3.5.3", "sass": "^1.89.1", "stylelint": "^16.20.0", "stylelint-config-standard": "^38.0.0", "stylelint-config-standard-scss": "^15.0.1", "stylelint-webpack-plugin": "^5.0.1"}}