// runner.js
const { spawn } = require('child_process');

const scripts = [
  { dir: './public/assets/icons', prefix: 'Icon' },
  { dir: './public/assets/placeholders', prefix: 'Placeholder' },
];

function runScript(dir) {
  return new Promise((resolve, reject) => {
    const child = spawn(
      'node',
      ['./scripts/generate-index.js', `--dir=${dir}`],
      {
        stdio: ['pipe', 'pipe', 'pipe'],
      },
    );

    let output = '';

    let error = '';

    child.stdout.on('data', (data) => {
      output += data.toString();
    });

    child.stderr.on('data', (data) => {
      error += data.toString();
    });

    child.on('close', (code) => {
      if (code === 0) {
        resolve({ dir, output });
      } else {
        reject({ dir, error, code });
      }
    });
  });
}

async function main() {
  try {
    const results = await Promise.allSettled(
      scripts.map((s) => runScript(s.dir)),
    );

    results.forEach((result) => {
      if (result.status === 'fulfilled') {
        console.log(`✅ Finished ${result.value.dir}`);
        console.log(result.value.output);
      } else {
        console.error(
          `❌ Failed ${result.reason.dir} (code ${result.reason.code})`,
        );
        console.error(result.reason.error);
      }
    });
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

main();
