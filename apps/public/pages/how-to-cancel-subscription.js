import { MainLayout, PageLayout } from '../components/Layouts';
import { Mo<PERSON>, Seo } from '../components';
import styles from '../styles/how-to-cancel-subscription.module.scss';
import { useEffect, useState } from 'react';
import { useViewport } from '../utils/useViewport';
import seoPageProps from '../utils/seoPageProps';
import Link from 'next/link';
import { isMobile } from '../utils/isMobile';
import { sharedServerSideProps } from '../utils/sharedServerSideProps';
import Image from 'next/image';
import desktopImage1 from '../public/assets/how-to-cancel-subscription/desktop/1.webp';
import desktopImage2 from '../public/assets/how-to-cancel-subscription/desktop/2.webp';
import desktopImage3 from '../public/assets/how-to-cancel-subscription/desktop/3.webp';
import desktopImage4 from '../public/assets/how-to-cancel-subscription/desktop/4.webp';
import desktopImage5 from '../public/assets/how-to-cancel-subscription/desktop/5.webp';
import desktopImage6 from '../public/assets/how-to-cancel-subscription/desktop/6.webp';
import mobileImage1 from '../public/assets/how-to-cancel-subscription/mobile/1.webp';
import mobileImage2 from '../public/assets/how-to-cancel-subscription/mobile/2.webp';
import mobileImage3 from '../public/assets/how-to-cancel-subscription/mobile/3.webp';
import mobileImage4 from '../public/assets/how-to-cancel-subscription/mobile/4.webp';
import mobileImage5 from '../public/assets/how-to-cancel-subscription/mobile/5.webp';
import mobileImage6 from '../public/assets/how-to-cancel-subscription/mobile/6.webp';

export const getServerSideProps = async ({ resolvedUrl, req, res }) => {
  const isMobileFromUserAgent = isMobile(req.headers['user-agent']);
  const sharedProps = await sharedServerSideProps(req, res);

  return {
    props: {
      ...sharedProps,
      seoPage: await seoPageProps(resolvedUrl),
      defaultIsMobile: isMobileFromUserAgent,
    },
  };
};

const desktopImages = {
  1: desktopImage1,
  2: desktopImage2,
  3: desktopImage3,
  4: desktopImage4,
  5: desktopImage5,
  6: desktopImage6,
};

const mobileImages = {
  1: mobileImage1,
  2: mobileImage2,
  3: mobileImage3,
  4: mobileImage4,
  5: mobileImage5,
  6: mobileImage6,
};

export default function HowToCancelSubscription({ seoPage, defaultIsMobile }) {
  const { width } = useViewport();
  const [isMobile, setIsMobile] = useState(defaultIsMobile);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [imagePreviewId, setImagePreviewId] = useState(null);

  useEffect(() => {
    if (width) {
      setIsMobile(width < 1024);
    }
  }, [width]);

  const openImagePreview = (id) => {
    setImagePreviewId(id);
    setShowImagePreview(true);
  };

  const closeImagePreview = () => {
    setShowImagePreview(false);
    setImagePreviewId(null);
  };

  return (
    <MainLayout
      isDefaultHeaderVisible
      isMobileMenuVisible
      isUserMenuVisible
      isSaleHeaderVisible
      isMobileSubscribeHeaderVisible
    >
      <PageLayout>
        <div className={styles.title}>
          <Seo seoPage={seoPage} />
        </div>
        <p>
          You can cancel allcasting subscription at any time by using one of the
          3 options:
        </p>
        <ul className={styles['option-list']}>
          <li className={styles['option-list-element']}>
            Discontinue your subscription online via Account Settings
          </li>
          <li className={styles['option-list-element']}>
            By email to our support team{' '}
            <Link href="mailto:<EMAIL>" className={styles.link}>
              <EMAIL>
            </Link>
          </li>
          <li className={styles['option-list-element']}>
            By phone{' '}
            <Link href="tel:+***********" className={styles.link}>
              **************
            </Link>
          </li>
        </ul>
        <h2 className={styles['sub-title']}>
          How to cancel allcasting subscription online?
        </h2>
        <ol className={styles['step-list']}>
          <li className={styles['step-list-element']}>
            Log in to allcasting.com with your username and password
          </li>
          <li className={styles['step-list-element']}>
            <p>Go to &quot;Account Settings&quot;</p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[1] : desktopImages[1]}
                onClick={() => openImagePreview(1)}
                alt=""
              />
            </div>
          </li>
          <li className={styles['step-list-element']}>
            <p>
              Choose &quot;Subscription&quot; under &quot;Billing
              Information&quot; and provide your password to access
            </p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[2] : desktopImages[2]}
                onClick={() => openImagePreview(2)}
                alt=""
              />
            </div>
          </li>
          <li className={styles['step-list-element']}>
            <p>Click on &quot;discontinue subscription&quot;</p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[3] : desktopImages[3]}
                onClick={() => openImagePreview(3)}
                alt=""
              />
            </div>
          </li>
          <li className={styles['step-list-element']}>
            <p>Enter your password to confirm</p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[4] : desktopImages[4]}
                onClick={() => openImagePreview(4)}
                alt=""
              />
            </div>
          </li>
          <li className={styles['step-list-element']}>
            <p>
              Provide a reason for discontinuing your allcasting subscription,
              check that you understand the consequences of this action and
              click on the red &quot;discontinue my subscription&quot; button
            </p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[5] : desktopImages[5]}
                onClick={() => openImagePreview(5)}
                alt=""
              />
            </div>
          </li>
          <li className={styles['step-list-element']}>
            <p>
              Once the subscription is canceled you&apos;ll see confirmation
              info and the last date of your subscription. You can still
              continue to use allcasting premium services until the last day of
              your subscription.
            </p>
            <div className={styles['image-container']}>
              <Image
                src={isMobile ? mobileImages[6] : desktopImages[6]}
                onClick={() => openImagePreview(6)}
                alt=""
              />
            </div>
          </li>
        </ol>
        <p>
          If you&apos;re experiencing problems with your subscriptions
          cancellation, please{' '}
          <Link href="/contact" className={styles.link}>
            contact our support team
          </Link>
          .
        </p>
        {showImagePreview && (
          <Modal
            backdropClose
            contentClose
            onClose={closeImagePreview}
            showDefaultLayout={false}
            showCloseButton={false}
            classNameContainer={styles['image-modal']}
            classNameContent={styles['image-modal-content']}
          >
            <div className={styles['image-preview-container']}>
              <div className={styles['image-preview']}>
                <Image
                  src={
                    isMobile
                      ? mobileImages[imagePreviewId]
                      : desktopImages[imagePreviewId]
                  }
                  alt=""
                />
              </div>
            </div>
          </Modal>
        )}
      </PageLayout>
    </MainLayout>
  );
}
