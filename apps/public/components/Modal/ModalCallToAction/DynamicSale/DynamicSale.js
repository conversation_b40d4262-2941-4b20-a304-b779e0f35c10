import React, { memo } from 'react';
import styles from './DynamicSale.module.scss';
import { Button } from '../../../index';
import { useCTA } from '../../../../contexts/CTAContext';
import { Amp } from '../../../../services/amp';
import useTrackElementActions from '../../../../utils/useTrackElementActions';
import { useAuth } from '../../../../contexts/AuthContext';
import { getImageProps } from 'next/image';
import coupleImageSource from '../../../../public/assets/guide-unit-modals/couple.webp';

const calculatePrice = (price, period, isDailyPrice, isWeeklyPrice) => {
  switch (true) {
    case isWeeklyPrice:
      const weekly = price / period / 4;

      return `${(Math.round(weekly * 100) / 100).toFixed(2)}/week`;
    case isDailyPrice:
      const daily = (price * (12 / period)) / 365;

      return `${(Math.round(daily * 100) / 100).toFixed(2)}/day`;
    default:
      return `${(price / period).toFixed(2).replace('.00', '')}/mo`;
  }
};

const DynamicSale = () => {
  const { modal, prices, isDailyPrice, isModalOverride, isWeeklyPrice } =
    useCTA();
  const { userProfiles } = useAuth();

  const {
    button_color,
    button_link,
    button_text,
    description,
    desktop_src,
    mobile_src,
    promo_text,
    promoted_period,
    promoted_period_free,
  } = modal || {};

  const version = isModalOverride ? 'discount' : 'default';
  const name = isModalOverride
    ? 'Discount manager popup'
    : 'Default subscribe popup';

  const {
    props: { srcSet: coupleImage, ...coupleImageRest },
  } = getImageProps({
    alt: 'Apply to casting calls',
    src: coupleImageSource,
  });

  const { onTrackClick } = useTrackElementActions({
    name,
    type: Amp.element.type.popup,
    context: Amp.element.context.ctaPopup,
    version,
    autoTrackEnabled: true,
  });

  const getPrice = () => {
    const { price } =
      prices.find(({ period }) => period === promoted_period) || {};
    const promoPeriod = promoted_period - promoted_period_free;
    const promoPrice = calculatePrice(
      price,
      promoPeriod,
      isDailyPrice,
      isWeeklyPrice,
    );

    return `${promoted_period / 12 === 1 ? '1 Year, $' : `${promoted_period / 12} Years, $`}${promoPrice}`;
  };

  const getDiscount = () => {
    const { price, base_month_price } =
      prices.find(({ period }) => period === promoted_period) || {};
    const fullPrice = base_month_price * promoted_period;

    return ((100 * (fullPrice - price)) / fullPrice).toFixed();
  };

  const onClick = () => {
    onTrackClick();

    window.location.href = `${process.env.redirectTalentUrl}${button_link || '/upgrade'}`;
  };

  const injectName = (text) => {
    if (text) {
      const firstName = userProfiles?.[0]?.firstName || 'Future Superstar';

      return text.replaceAll('[Name]', firstName);
    } else {
      return '';
    }
  };

  return (
    <>
      <picture>
        <source
          srcSet={desktop_src || coupleImage}
          media="(min-width: 620px)"
        />
        <img
          src={mobile_src || undefined}
          alt="Apply to casting calls"
          {...(!mobile_src ? coupleImageRest : {})}
          className={styles.image}
          onClick={onClick}
        />
      </picture>
      <div className={styles.container}>
        <div className={styles.title} onClick={onClick}>
          {prices?.length > 0 && (promo_text || promoted_period) ? (
            <>{injectName(promo_text) || `${getDiscount()}% OFF`}</>
          ) : (
            'Open the Door!'
          )}
        </div>
        {prices?.length > 0 && promoted_period && (
          <div className={styles.price} onClick={onClick}>
            {getPrice()}
          </div>
        )}
        <div className={styles.description}>
          {injectName(description) || (
            <>
              Get exclusive access to casting calls! <br />
              This is your key to land top gigs and make a splash in the
              industry.
            </>
          )}
        </div>
        <Button
          label={button_text || 'Get The Deal'}
          minWidth="220px"
          color={button_color || 'pink'}
          shadow={false}
          onClick={onClick}
        />
      </div>
    </>
  );
};

export default memo(DynamicSale);
