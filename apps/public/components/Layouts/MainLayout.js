import { useEffect, useState } from 'react';
import styles from './MainLayout.module.scss';
import {
  <PERSON><PERSON>,
  Footer,
  MobileMenu,
  MobileSidebarMenu,
  UserMenu,
  SaleHeader,
  Notification,
  Modal,
  LiveStream,
  SubscribeHeader,
  PremiumHeader,
  PremiumActions,
  ModalLogin,
  ModalRegistration,
  TalentMobileMenu,
  UINotificationsModals,
  LogoHeader,
  ModalCallToAction,
  LiveChat,
} from '..';
import { useAuth } from '../../contexts/AuthContext';
import { ModalProvider } from '../../contexts/ModalContext';
import { useSale } from '../../contexts/SaleContext';
import { useNotifications } from '../../contexts/NotificationContext';
import { liveStreamTime } from '../../utils/liveStreamHelper';
import { useFeature } from '../../contexts/FeatureContext';
import { UINotificationsProvider } from '../../contexts/UINotificationsContext';
import { CTAModalProvider } from '../../contexts/CTAModalContext';

export default function MainLayout({
  children,
  isDefaultHeaderVisible = false,
  isMobileMenuVisible = false,
  isPremiumHeaderVisible = false,
  isUserMenuVisible = false,
  isSaleHeaderVisible = false,
  isMobileSubscribeHeaderVisible = false,
  isFooterVisible = true,
  isLogoHeaderVisible = false,
}) {
  const {
    isAuthenticated,
    userType,
    accountLevel,
    authLogoutWithTracking,
    userProfiles,
    redirectURL,
  } = useAuth();
  const { saleExpirationTime, showSale, stopSale } = useSale();
  const { showNotification, setNotification } = useNotifications();
  const [showSideBar, setShowSideBar] = useState(false);
  const [showRegistrationModal, setShowRegistrationModal] = useState(false);
  const [showAgentRegistrationModal, setShowAgentRegistrationModal] =
    useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [showHelpCenter, setShowHelpCenter] = useState(false);
  const [showLiveStream, setLiveStream] = useState(
    process.env.liveStreamEnabled === 'true' && liveStreamTime,
  );

  const { premiumActionsButtonEnabled } = useFeature();

  useEffect(() => {
    const currenRef = document;
    const callback = () => setShowHelpCenter(false);

    if (showHelpCenter) {
      currenRef.addEventListener('mouseup', callback);

      return () => currenRef.removeEventListener('mouseup', callback);
    }
  }, [showHelpCenter]);

  const toggleShowBarVisible = () => setShowSideBar(!showSideBar);

  const toggleShowLoginModal = () => {
    if (!showLoginModal && !navigator.cookieEnabled) {
      setNotification({
        type: 'error',
        message:
          'Please enable Cookies to be able to use full website functionality',
        displayModal: true,
      });

      return;
    }
    setShowLoginModal(!showLoginModal);
  };

  const toggleShowRegistrationModal = () => {
    if (!showRegistrationModal && !navigator.cookieEnabled) {
      setNotification({
        type: 'error',
        message:
          'Please enable Cookies to be able to use full website functionality',
        displayModal: true,
      });

      return;
    }
    setShowRegistrationModal(!showRegistrationModal);
    setShowAgentRegistrationModal(false);
  };

  const toggleShowAgentRegistrationModal = () => {
    setShowRegistrationModal(!showRegistrationModal);
    setShowAgentRegistrationModal(true);
  };

  const toggleHelpCenter = () => setShowHelpCenter(!showHelpCenter);

  const closeLiveStream = () => {
    setLiveStream(false);
  };

  const isTalent = userType === 'talent';

  return (
    <ModalProvider
      value={{
        onLoginModalOpen: toggleShowLoginModal,
        onSignUpModalOpen: toggleShowRegistrationModal,
        onAgentSignupModalOpen: toggleShowAgentRegistrationModal,
      }}
    >
      <section className={styles['header-section']}>
        {isAuthenticated &&
          !showSale &&
          isTalent &&
          (accountLevel?.canUpgradeExistingSubscription ||
            !accountLevel?.isPaidOrDelayed) &&
          isMobileSubscribeHeaderVisible && (
            <SubscribeHeader
              canUpgradeExistingSubscription={
                accountLevel?.canUpgradeExistingSubscription
              }
            />
          )}
        {showSale && isSaleHeaderVisible && (
          <SaleHeader
            saleExpirationTime={saleExpirationTime}
            onStopSale={stopSale}
          />
        )}
        <div className={styles['header-section-headers']}>
          {isDefaultHeaderVisible && (
            <Header
              isAuthenticated={isAuthenticated}
              isTalent={isTalent}
              isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
              canUpgradeExistingSubscription={
                !!accountLevel?.canUpgradeExistingSubscription
              }
              showSignUpModal={toggleShowRegistrationModal}
              showLoginModal={toggleShowLoginModal}
            />
          )}
        </div>
        {isLogoHeaderVisible && <LogoHeader />}
        {isPremiumHeaderVisible && <PremiumHeader />}
        {showNotification?.message && !showNotification?.showOnModal && (
          <Notification
            type={showNotification?.type}
            onClose={() => setNotification({ message: null })}
            timeout={showNotification?.timeout}
            displayModal={showNotification?.displayModal}
            message={showNotification?.message}
          />
        )}
      </section>
      {isAuthenticated && isUserMenuVisible && (
        <UserMenu
          stopSale={stopSale}
          showSale={showSale}
          showHelpCenter={showHelpCenter}
          toggleHelpCenter={toggleHelpCenter}
          userProfiles={userProfiles}
          authLogout={authLogoutWithTracking}
          redirectURL={redirectURL}
        />
      )}
      <div>
        <div>
          {children}
          {isFooterVisible && <Footer />}
        </div>
        {isMobileMenuVisible && (
          <>
            {isTalent ? (
              <TalentMobileMenu
                profile={userProfiles[0]}
                className={styles['mobile-menu']}
                onSideBarOpen={toggleShowBarVisible}
              />
            ) : (
              <MobileMenu
                className={styles['mobile-menu']}
                onSideBarOpen={toggleShowBarVisible}
              />
            )}
          </>
        )}
        {showSideBar && (
          <Modal
            backdropClose
            onClose={toggleShowBarVisible}
            showDefaultLayout={false}
            showCloseButton={false}
            showAnimation={false}
          >
            <MobileSidebarMenu
              open={showSideBar}
              onShowSignUpModal={toggleShowRegistrationModal}
              onShowLoginModal={toggleShowLoginModal}
              onSideBarClose={toggleShowBarVisible}
              isAuthenticated={isAuthenticated}
              profile={userProfiles[0]}
              isTalent={isTalent}
              isPaidOrDelayed={!!accountLevel?.isPaidOrDelayed}
              canUpgradeExistingSubscription={
                !!accountLevel?.canUpgradeExistingSubscription
              }
              gender={userProfiles[0]?.gender}
              accountLevel={accountLevel}
              authLogout={authLogoutWithTracking}
              showSale={showSale}
              stopSale={stopSale}
              saleExpirationTime={saleExpirationTime}
              redirectURL={redirectURL}
            />
          </Modal>
        )}
      </div>

      {showRegistrationModal && (
        <ModalRegistration
          onClose={toggleShowRegistrationModal}
          showAgentRegistrationModal={showAgentRegistrationModal}
        />
      )}

      {showLoginModal && (
        <ModalLogin
          onClose={toggleShowLoginModal}
          openRegistrationModal={toggleShowRegistrationModal}
        />
      )}

      {showLiveStream && (
        <LiveStream
          title="How to become a Voice-Over actor for Cartoons?"
          video="https://www.youtube.com/embed/7hvecZ9jNjU"
          onClose={closeLiveStream}
          autoplay
        />
      )}

      <LiveChat />

      {premiumActionsButtonEnabled && <PremiumActions isMobile />}

      {isAuthenticated && isTalent && (
        <UINotificationsProvider>
          <UINotificationsModals />
        </UINotificationsProvider>
      )}

      {isAuthenticated && isTalent && !accountLevel?.isPaidOrDelayed && (
        <CTAModalProvider>
          <ModalCallToAction />
        </CTAModalProvider>
      )}
    </ModalProvider>
  );
}
