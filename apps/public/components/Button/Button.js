import React, { memo } from 'react';
import cn from 'classnames';
import styles from './Button.module.scss';
import Link from 'next/link';

const Button = ({
  label = '',
  kind = 'primary', // primary | secondary
  type = 'button', // button | link | submit | reset
  href = '#',
  color = 'blue', // blue | orange | greenblue | green-gradient | midnight | grey | purple | pink | white | red
  disabled = false,
  minWidth,
  lowercase,
  shadow = true,
  onClick,
  className,
  ariaLabel,
  isSEO,
  dataCy,
  target = '_self',
}) => {
  return (
    <>
      {type === 'link' ? (
        <Link
          href={href}
          onClick={onClick}
          className={cn(styles.btn, styles[color], className, {
            [styles.primary]: kind === 'primary',
            [styles.secondary]: kind === 'secondary',
            [styles.shadow]: shadow,
            [styles.lowercase]: lowercase,
            [styles.disabled]: disabled,
          })}
          style={{ minWidth }}
          aria-label={ariaLabel}
          data-cy={dataCy}
          target={target}
        >
          {isSEO ? <h1>{label}</h1> : label}
        </Link>
      ) : (
        <button
          type={type}
          className={cn(styles.btn, styles[color], className, {
            [styles.primary]: kind === 'primary',
            [styles.secondary]: kind === 'secondary',
            [styles.shadow]: shadow,
            [styles.lowercase]: lowercase,
            [styles.disabled]: disabled,
          })}
          disabled={disabled}
          style={{ minWidth }}
          onClick={onClick}
          data-cy={dataCy}
        >
          {isSEO ? <h1>{label}</h1> : label}
        </button>
      )}
    </>
  );
};

export default memo(Button);
