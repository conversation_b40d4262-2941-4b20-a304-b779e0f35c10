import styles from './CallToActionBlock.module.scss';
import { useEffect, useState } from 'react';
import ClapperboardIcon from '../../public/assets/icons/icon-clapperboard.svg';
import SendMessageIcon from '../../public/assets/icons/icon-send-message.svg';
import { useRouter } from 'next/router';
import { useAuth } from '../../contexts/AuthContext';
import { Amp } from '../../services/amp';
import cn from 'classnames';
import { Button, ElementViewed } from '../index';
import {
  ctaBasicUserMessages,
  ctaGuestMessages,
} from '../../constants/callToActionBlock';
import useTrackElementActions from '../../utils/useTrackElementActions';
import { SubscriptionBlastBanner } from './SubscriptionBlastBanner';

/**
 * @param {Object} props
 * @param {boolean} [props.list]
 * @param {boolean} [props.listGrid]
 * @param {string} [props.position]
 * @param {string} [props.className]
 */
const CallToActionBlock = ({
  list = false,
  listGrid = false,
  position,
  className,
}) => {
  const [version, setVersion] = useState(null);

  const { accountLevel, userType } = useAuth();
  const router = useRouter();

  const isBasicTalent = userType === 'talent' && !accountLevel?.isPaidOrDelayed;
  const name = isBasicTalent
    ? 'Basic talent subscribe block'
    : 'Guest registration block';

  const buttonLabel = isBasicTalent ? 'pick a plan' : 'sign up';

  const { onTrackClick } = useTrackElementActions({
    name,
    type: Amp.element.type.block,
    context: Amp.element.context.ctaBlock,
    position,
    id: version?.id,
    autoTrackEnabled: false,
  });

  useEffect(() => {
    const options = isBasicTalent ? ctaBasicUserMessages : ctaGuestMessages;
    const option = options[Math.floor(Math.random() * options.length)];

    if (option) {
      setVersion({ ...option, icon: Math.round(Math.random()) });
    }
  }, [isBasicTalent]);

  const onCallToActionClick = () => {
    onTrackClick();

    if (isBasicTalent) {
      window.location.href = `${process.env.redirectTalentUrl}/upgrade`;
    } else {
      router.push(`/register`);
    }
  };

  return (
    <>
      {!!version && (
        <div
          className={cn({
            [styles.grid]: !!listGrid,
            [styles['injected-in-list']]: !!list,
            [styles['injected-in-header']]: !list,
            [className]: !!className,
          })}
        >
          {version.id !== 'cta_basic_11' ? (
            <div className={styles['interactive-container']}>
              <div className={styles['interactive-content']}>
                <div className={styles.header}>{version.title}</div>
                <div className={styles.description}>
                  {version.icon ? (
                    <ClapperboardIcon className={styles.icon} />
                  ) : (
                    <SendMessageIcon className={styles.icon} />
                  )}
                  <span>{version.description}</span>
                </div>
              </div>
              <div className={styles['btn-container']}>
                <ElementViewed
                  name={name}
                  type={Amp.element.type.block}
                  context={Amp.element.context.ctaBlock}
                  id={version.id}
                  position={position}
                >
                  <Button
                    color="purple"
                    label={buttonLabel}
                    minWidth="180px"
                    className={styles['cta-button']}
                    onClick={onCallToActionClick}
                    shadow={false}
                  />
                </ElementViewed>
              </div>
            </div>
          ) : (
            <div
              className={styles['subscription-blast']}
              onClick={onCallToActionClick}
            >
              <div className={styles['subscription-blast-image-container']}>
                <SubscriptionBlastBanner listGrid={listGrid} />
              </div>
              <ElementViewed
                name={name}
                type={Amp.element.type.block}
                context={Amp.element.context.ctaBlock}
                id={version.id}
                position={position}
              >
                <Button
                  color="purple"
                  label={buttonLabel}
                  minWidth="180px"
                  className={cn(styles['cta-blast-button'])}
                  onClick={onCallToActionClick}
                  shadow={false}
                />
              </ElementViewed>
            </div>
          )}
        </div>
      )}
    </>
  );
};

export default CallToActionBlock;
